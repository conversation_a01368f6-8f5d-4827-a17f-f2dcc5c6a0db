#!/usr/bin/env python3
"""
Cleanup script to remove duplicate followup task sets.
This script will help clean up the duplicate level 1, 2, 3 task sets that were created.
"""

import asyncio
import os
import sys
from bson import ObjectId

# Add the app directory to the Python path
sys.path.append('/home/<USER>/Documents/nextai/nepali_app')

async def cleanup_duplicate_followups():
    """Clean up duplicate followup task sets."""
    
    print("🧹 Cleanup script for duplicate followup task sets")
    print("")
    print("📋 To clean up duplicates manually:")
    print("1. Connect to your MongoDB database")
    print("2. Run this query to find duplicates:")
    print("")
    print("   db.task_sets.aggregate([")
    print("     {")
    print("       $match: {")
    print("         gentype: 'follow_up',")
    print("         followup_count: { $exists: true }")
    print("       }")
    print("     },")
    print("     {")
    print("       $group: {")
    print("         _id: {")
    print("           original_task_set_id: '$original_task_set_id',")
    print("           followup_count: '$followup_count'")
    print("         },")
    print("         count: { $sum: 1 },")
    print("         docs: { $push: '$_id' }")
    print("       }")
    print("     },")
    print("     {")
    print("       $match: {")
    print("         count: { $gt: 1 }")
    print("       }")
    print("     }")
    print("   ])")
    print("")
    print("3. For each duplicate group, delete all but one:")
    print("")
    print("   // Keep the first one, delete the rest")
    print("   db.task_sets.deleteMany({")
    print("     _id: { $in: [ObjectId('duplicate_id_2'), ObjectId('duplicate_id_3')] }")
    print("   })")
    print("")
    print("4. Also delete the associated task_items:")
    print("")
    print("   db.task_items.deleteMany({")
    print("     task_set_id: { $in: [ObjectId('duplicate_id_2'), ObjectId('duplicate_id_3')] }")
    print("   })")
    print("")
    print("✅ After cleanup, the sequential followup generation will work correctly:")
    print("   - Original completed → Level 1 only")
    print("   - Level 1 completed → Level 2 only") 
    print("   - Level 2 completed → Level 3 only")

if __name__ == "__main__":
    asyncio.run(cleanup_duplicate_followups())
