#!/usr/bin/env python3
"""
Test script to verify EdgeTTS integration and robust audio format detection
across all audio generation services.
"""

import sys
import os
import asyncio

# Add the app directory to the Python path
sys.path.append(os.path.join(os.path.dirname(__file__), 'app'))

async def test_edgetts_generation():
    """Test EdgeTTS audio generation directly."""
    print("Testing EdgeTTS Audio Generation")
    print("=" * 50)
    
    try:
        from app.v2.api.socket_service_v2.generator.edgettsgen import generate_audio_bytes
        
        # Test Nepali text
        nepali_text = "नमस्ते, यो एक परीक्षण हो।"
        print(f"Generating audio for Nepali text: {nepali_text}")
        
        audio_data = await generate_audio_bytes(
            text=nepali_text,
            language="ne",
            gender="Female"
        )
        
        if audio_data:
            print(f"✅ Successfully generated {len(audio_data)} bytes of audio")
            print(f"First 10 bytes: {audio_data[:10]}")
            
            # Test format detection
            from app.v2.api.socket_service_v2.generator.audiogen import detect_audio_format
            file_extension, content_type = detect_audio_format(audio_data)
            print(f"Detected format: {file_extension} ({content_type})")
            
            return True
        else:
            print("❌ Failed to generate audio")
            return False
            
    except Exception as e:
        print(f"❌ Error testing EdgeTTS: {e}")
        return False


async def test_editor_service_audio():
    """Test editor service audio generation."""
    print("\n\nTesting Editor Service Audio Generation")
    print("=" * 50)
    
    try:
        from app.v2.api.editor_service.generators.audiogen import generate
        
        test_content = "Hello, this is a test for editor service audio generation."
        print(f"Generating audio for: {test_content}")
        
        audio_data, usage_metadata = await generate(test_content)
        
        if audio_data:
            print(f"✅ Successfully generated {len(audio_data)} bytes of audio")
            print(f"Usage metadata: {usage_metadata}")
            
            # Test format detection
            from app.v2.api.editor_service.generators.audiogen import detect_audio_format
            file_extension, content_type = detect_audio_format(audio_data)
            print(f"Detected format: {file_extension} ({content_type})")
            
            return True
        else:
            print("❌ Failed to generate audio")
            return False
            
    except Exception as e:
        print(f"❌ Error testing editor service audio: {e}")
        return False


def test_format_detection_consistency():
    """Test that format detection is consistent across all services."""
    print("\n\nTesting Format Detection Consistency")
    print("=" * 50)
    
    try:
        # Import all detect_audio_format functions
        from app.v1.api.socket_service.generator.audiogen import detect_audio_format as v1_detect
        from app.v2.api.socket_service_v2.generator.audiogen import detect_audio_format as v2_socket_detect
        from app.v2.api.editor_service.generators.audiogen import detect_audio_format as v2_editor_detect
        from app.v2.api.editor_service.generators.tasks.task_audio import detect_audio_format as v2_task_detect
        
        # Test data samples
        test_samples = [
            ("WAV", b'RIFF\x24\x08\x00\x00WAVE'),
            ("MP3 ID3", b'ID3\x03\x00\x00\x00\x00\x00\x00'),
            ("MP3 Frame", b'\xFF\xFB\x90\x00\x00\x00\x00\x00'),
            ("FLAC", b'fLaC\x00\x00\x00\x22\x10\x00'),
            ("OGG", b'OggS\x00\x02\x00\x00\x00\x00'),
        ]
        
        all_consistent = True
        
        for sample_name, sample_data in test_samples:
            print(f"\nTesting {sample_name} format:")
            
            # Test all detection functions
            v1_result = v1_detect(sample_data)
            v2_socket_result = v2_socket_detect(sample_data)
            v2_editor_result = v2_editor_detect(sample_data)
            v2_task_result = v2_task_detect(sample_data)
            
            print(f"  V1 Socket:     {v1_result}")
            print(f"  V2 Socket:     {v2_socket_result}")
            print(f"  V2 Editor:     {v2_editor_result}")
            print(f"  V2 Task:       {v2_task_result}")
            
            # Check consistency
            if v1_result == v2_socket_result == v2_editor_result == v2_task_result:
                print(f"  ✅ All consistent")
            else:
                print(f"  ❌ Inconsistent results!")
                all_consistent = False
        
        return all_consistent
        
    except Exception as e:
        print(f"❌ Error testing format detection consistency: {e}")
        return False


async def main():
    """Run all tests."""
    print("EdgeTTS Integration and Audio Format Detection Test Suite")
    print("=" * 70)
    
    results = []
    
    # Test EdgeTTS generation
    results.append(await test_edgetts_generation())
    
    # Test editor service audio
    results.append(await test_editor_service_audio())
    
    # Test format detection consistency
    results.append(test_format_detection_consistency())
    
    # Summary
    print("\n" + "=" * 70)
    print("Test Results Summary:")
    print(f"EdgeTTS Generation: {'✅ PASSED' if results[0] else '❌ FAILED'}")
    print(f"Editor Service Audio: {'✅ PASSED' if results[1] else '❌ FAILED'}")
    print(f"Format Detection Consistency: {'✅ PASSED' if results[2] else '❌ FAILED'}")
    
    overall_success = all(results)
    print(f"\nOverall: {'🎉 ALL TESTS PASSED' if overall_success else '⚠️ SOME TESTS FAILED'}")
    
    if overall_success:
        print("\n✅ Audio generation is now unified:")
        print("• All services use EdgeTTS for consistent audio generation")
        print("• Robust format detection across all audio generators")
        print("• Support for WAV, MP3, FLAC, OGG, AAC, M4A, AIFF, WebM, AMR, 3GP")
        print("• Fallback to WAV for unknown formats")
        print("• Consistent behavior across V1, V2 socket, and editor services")
    else:
        print("\n⚠️ Please review and fix the failing tests.")
    
    return overall_success


if __name__ == "__main__":
    asyncio.run(main())
