#!/usr/bin/env python3
"""
Test script to debug followup generation issues.
This script will help identify why followup generation is not working.
"""

import asyncio
import os
import sys
from bson import ObjectId

# Add the app directory to the Python path
sys.path.append('/home/<USER>/Documents/nextai/nepali_app')

from app.shared.models.user import UserTenantDB
from app.v2.api.socket_service_v2.generator.folllowup import followup_generate

async def test_followup_generation():
    """Test followup generation with a sample task set."""
    
    print("🚀 Starting followup generation test...")
    
    # You'll need to replace these with actual values from your database
    # Get a completed task set ID from your database
    task_set_id = "REPLACE_WITH_ACTUAL_TASK_SET_ID"  # Replace with real ID
    
    # Create a mock user tenant (you'll need to adjust this based on your setup)
    # This is just for testing - replace with actual user setup
    print("❌ This is a template test script.")
    print("📝 To use this script:")
    print("1. Replace 'REPLACE_WITH_ACTUAL_TASK_SET_ID' with a real completed task set ID")
    print("2. Set up proper UserTenantDB connection")
    print("3. Run the script to test followup generation")
    print("")
    print("🔍 Check the logs in your application to see:")
    print("   - If task completion is detected")
    print("   - If followup generation is triggered")
    print("   - Any errors in the followup generation process")
    print("")
    print("📋 Key things to check:")
    print("   - Task set gentype should be 'primary' or 'follow_up'")
    print("   - attempted_tasks should equal total_tasks")
    print("   - followup_generator_count should be > 0 (default: 3)")
    print("   - Original task set should have input_content with audio")

if __name__ == "__main__":
    asyncio.run(test_followup_generation())
