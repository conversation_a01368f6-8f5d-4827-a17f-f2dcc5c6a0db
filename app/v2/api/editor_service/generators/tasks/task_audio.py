# To run this code you need to install the following dependencies:
# pip install google-genai

import struct
from typing import Union
from app.shared.models.user import UserTenantDB
from app.shared.utils.logger import setup_new_logging
import hashlib
from app.v2.api.socket_service_v2.generator.edgettsgen import generate_audio_bytes
from app.v2.api.socket_service_v2.generator.audiogen import detect_audio_format
loggers = setup_new_logging(__name__)


async def generate(current_user: UserTenantDB, keyword: Union[str, list[str]], type: str = "audio_prompt"):
    """
    Generate audio for keywords using EdgeTTS.

    Args:
        current_user: User database connection
        keyword: Text to convert to audio (string or list of strings)
        type: Type of prompt (not used with EdgeTTS but kept for compatibility)

    Returns:
        Tuple of (file_text, file_info, usage_metadata)
    """
    if isinstance(keyword, list):
        keyword = ", ".join(keyword)

    loggers.info(f"Generating audio for keyword: {keyword}")

    # Generate audio using EdgeTTS
    file_bytes = await generate_audio_bytes(
        text=keyword,
        language="ne",  # Nepali language
        gender="Female"
    )

    if not file_bytes:
        loggers.error(f"No audio data received for: {keyword}")
        return None, None, {}

    # Determine file extension and content type using robust format detection
    file_extension, content_type = detect_audio_format(file_bytes)
    loggers.info(f"Detected audio format: {file_extension} ({content_type}) for keyword: {keyword}")

    # Save to MinIO
    from app.shared.async_minio_client import create_async_minio_client

    async_minio_client = create_async_minio_client(current_user.minio)
    file_info = await async_minio_client.save_file_async(
        data=file_bytes,
        user_id=current_user.user.id,
        content_type=content_type,
        folder="audiogen",
        file_extension=file_extension,
        # custom_filename=hashlib.sha256(f"{keyword}_audio{file_extension}").hexdigest()[:10] 
        custom_filename=hashlib.sha256(f"{keyword}_audio{file_extension}".encode()).hexdigest()[:10] + file_extension
    )

    loggers.info(f"Audio generated and saved: {keyword}")

    # EdgeTTS doesn't provide text output or usage metadata, so return empty values
    file_text = ""
    usage_metadata = {}

    return file_text, file_info, usage_metadata





if __name__ == "__main__":
    # For testing without user context, create a simple test
    print("Audio generation function updated to work with user context and Minio")
    print("Use this function in the task generation system with proper UserTenantDB context")