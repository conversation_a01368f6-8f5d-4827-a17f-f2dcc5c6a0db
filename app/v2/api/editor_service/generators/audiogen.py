# To run this code you need to install the following dependencies:
# pip install google-genai

import struct
from typing import <PERSON><PERSON>
from app.v2.api.socket_service_v2.generator.edgettsgen import generate_audio_bytes


def detect_audio_format(audio_data: bytes) -> Tuple[str, str]:
    """
    Detect audio format from binary data using magic bytes and file signatures.

    Args:
        audio_data: The audio data as bytes

    Returns:
        Tuple of (file_extension, content_type)
    """
    if not audio_data or len(audio_data) < 12:
        print("Audio data too short for format detection, defaulting to WAV")
        return ".wav", "audio/wav"

    # WAV format detection (RIFF + WAVE)
    if audio_data.startswith(b'RIFF') and audio_data[8:12] == b'WAVE':
        return ".wav", "audio/wav"

    # MP3 format detection
    # ID3v2 tag
    if audio_data.startswith(b'ID3'):
        return ".mp3", "audio/mpeg"

    # MP3 frame sync (11 bits set to 1)
    if len(audio_data) >= 2:
        # Check for MP3 frame header patterns
        if (audio_data[0] == 0xFF and (audio_data[1] & 0xE0) == 0xE0):
            return ".mp3", "audio/mpeg"

    # FLAC format detection
    if audio_data.startswith(b'fLaC'):
        return ".flac", "audio/flac"

    # OGG format detection
    if audio_data.startswith(b'OggS'):
        return ".ogg", "audio/ogg"

    # AAC format detection (ADTS)
    if len(audio_data) >= 2 and audio_data[0] == 0xFF and (audio_data[1] & 0xF0) == 0xF0:
        return ".aac", "audio/aac"

    # M4A/MP4 audio format detection
    if len(audio_data) >= 8:
        # Check for ftyp box in MP4 container
        if audio_data[4:8] == b'ftyp':
            # Check for M4A specific brands
            if b'M4A ' in audio_data[8:20] or b'mp41' in audio_data[8:20] or b'mp42' in audio_data[8:20]:
                return ".m4a", "audio/mp4"

    # AIFF format detection
    if audio_data.startswith(b'FORM') and len(audio_data) >= 12 and audio_data[8:12] == b'AIFF':
        return ".aiff", "audio/aiff"

    elif audio_data[:2] in [b'\xFF\xFB', b'\xFF\xF3', b'\xFF\xF2']:  # Raw MP3 frames
        return ".mp3", "audio/mpeg"

    # WebM audio format detection
    if audio_data.startswith(b'\x1A\x45\xDF\xA3'):
        return ".webm", "audio/webm"

    # AMR format detection
    if audio_data.startswith(b'#!AMR'):
        return ".amr", "audio/amr"

    # 3GP format detection
    if len(audio_data) >= 8 and audio_data[4:8] == b'ftyp':
        if b'3gp' in audio_data[8:20]:
            return ".3gp", "audio/3gpp"

    # If no format detected, check if it looks like raw PCM data
    # This is a heuristic - if the data doesn't match any known format
    # but has reasonable size and no obvious text content, assume it's raw audio
    if len(audio_data) > 1000:
        # Check if data contains mostly binary content (not text)
        try:
            audio_data[:100].decode('utf-8')
            # If it decodes as text, it's probably not audio
            print("Audio data appears to be text, defaulting to WAV")
        except UnicodeDecodeError:
            # If it doesn't decode as text, it's likely binary audio data
            print("Unknown audio format detected, treating as raw PCM and converting to WAV")
            return ".wav", "audio/wav"

    # Default fallback
    print(f"Could not detect audio format from {len(audio_data)} bytes, defaulting to WAV")
    return ".wav", "audio/wav"


def save_binary_file(file_name, data):
    f = open(file_name, "wb")
    f.write(data)
    f.close()
    print(f"File saved to to: {file_name}")


async def generate(content: str):
    """
    Generate audio from text using EdgeTTS.

    Args:
        content: Text content to convert to audio

    Returns:
        Tuple of (audio_data_bytes, usage_metadata)
    """
    # Generate audio using EdgeTTS
    audio_data = await generate_audio_bytes(
        text=content,
        language="ne",  # Nepali language
        gender="Female"
    )

    if not audio_data:
        print(f"Failed to generate audio for content: {content[:50]}...")
        return None, {}

    # EdgeTTS returns MP3 format, but we'll detect the actual format
    file_extension, content_type = detect_audio_format(audio_data)
    print(f"Generated audio format: {file_extension} ({content_type}) for content: {content[:50]}...")

    # Return audio data and empty usage metadata (EdgeTTS doesn't provide usage stats)
    return audio_data, {}


if __name__ == "__main__":
    content="""\"ए अर्जुन, कस्तो छ तिम्रो पढाइ? अँ, फेवा तालको छेउमा बस्नेलाई त डुङ्गा चलाउन कत्ति मन पर्छ होला, है? मलाई याद छ, तिमी सानो हुँदा, म तिमीलाई काँधमा बोकेर तालको किनारमा घुमाउँथेँ। अनि तिमी ' Gurkha soldier ' को कथा सुन्न कति मरिहत्ते गर्थ्यौ।\\n\\nआज म तिमीलाई एउटा साहसी Gurkha soldier को कथा सुनाउँछु। धेरै वर्ष अघिको कुरा हो, एउटा गाउँमा एउटा सानो केटो थियो। उसको नाम Biraj थियो। Biraj गरिब परिवारबाट थियो, तर उसको मनमा देशको लागि केही गर्ने ठूलो इच्छा थियो। ऊ जहिले पनि Gurkha soldier हरूको बारेमा कथाहरू सुन्थ्यो, उनीहरूको बहादुरी र साहसको बारेमा।\\n\\nएक दिन, Biraj ले Gurkha army मा भर्ती हुने निर्णय गर्यो। उसले धेरै अभ्यास गर्यो, दौडियो, उफ्र्यो, र लड्यो। उसको लगन देखेर गाउँका मानिसहरू पनि चकित भए। भर्तीको दिन आयो, Biraj अलिकति डराएको थियो, तर उसको मनमा देशको सेवा गर्ने ठूलो आत्मविश्वास थियो।\\n\\nउसले भर्ती परीक्षा पास गर्यो र Gurkha army मा सामेल भयो। उसले धेरै युद्धहरूमा लड्यो र आफ्नो बहादुरी देखायो। एक युद्धमा, उसको साथी घाइते भयो। Biraj ले आफ्नो ज्यान जोखिममा राखेर उसलाई बचायो। उसको साहस देखेर सबैजना चकित भए। Biraj एउटा सच्चा Gurkha soldier बन्यो।\\n\\nत्यसैले अर्जुन, Gurkha soldier बन्नलाई बल मात्र होइन, साहस र अरूको लागि केही गर्ने भावना पनि चाहिन्छ। तिमी पनि Biraj जस्तै साहसी बन्न सक्छौ। सधैं अरूलाई मद्दत गर र आफ्नो देशलाई माया गर। के छ, तिमी पनि एक दिन ठूलो मान्छे बन्छौ। अब जाऊ, डुङ्गा चलाएर आऊ। तर होशियार है!\"
"""
    generate(content)
